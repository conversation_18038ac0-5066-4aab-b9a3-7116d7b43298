import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/product_model.dart';
import '../services/upi_payment_service.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';
import '../widgets/order_confirmation_dialog.dart';

class PaymentScreen extends StatefulWidget {
  final List<Product> products;
  final double totalAmount;

  const PaymentScreen({
    super.key,
    required this.products,
    required this.totalAmount,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  String _selectedPaymentMethod = 'phonepe';
  bool _isProcessing = false;
  final _phoneController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Pre-fill phone number if available
    final user = FirebaseAuth.instance.currentUser;
    if (user?.phoneNumber != null) {
      _phoneController.text = user!.phoneNumber!;
    }
  }

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
        title: Text('Payment'),
        elevation: 0,
      ),
      body: ChickenImageWatermark(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Order Summary
                    _buildOrderSummary(),
                    SizedBox(height: 24),

                    // Payment Methods
                    _buildPaymentMethods(),
                    SizedBox(height: 24),

                    // Phone Number Input
                    _buildPhoneInput(),
                    SizedBox(height: 24),

                    // Payment Details
                    _buildPaymentDetails(),
                  ],
                ),
              ),
            ),

            // Pay Now Button
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isProcessing ? null : _processPayment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade400,
                    padding: EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isProcessing
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            ),
                            SizedBox(width: 12),
                            Text(
                              'Processing...',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        )
                      : Text(
                          'Pay ₹${widget.totalAmount.toStringAsFixed(2)}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order Summary',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            ...widget.products.map((product) => Padding(
              padding: EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '${product.name} x ${product.quantity}',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                  Text(
                    '₹${(product.price * product.quantity).toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            )).toList(),
            Divider(height: 20),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Total Amount',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Text(
                  '₹${widget.totalAmount.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Methods',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            _buildPaymentOption(
              'phonepe',
              'PhonePe',
              Icons.phone_android,
              Colors.purple,
            ),
            _buildPaymentOption(
              'googlepay',
              'Google Pay',
              Icons.payment,
              Colors.blue,
            ),
            _buildPaymentOption(
              'paytm',
              'Paytm',
              Icons.account_balance_wallet,
              Colors.indigo,
            ),
            _buildPaymentOption(
              'upi',
              'Other UPI Apps',
              Icons.account_balance,
              Colors.teal,
            ),
            _buildPaymentOption(
              'cod',
              'Cash on Delivery',
              Icons.money,
              Colors.green,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentOption(String value, String title, IconData icon, Color color) {
    return RadioListTile<String>(
      value: value,
      groupValue: _selectedPaymentMethod,
      onChanged: (String? newValue) {
        setState(() {
          _selectedPaymentMethod = newValue!;
        });
      },
      title: Row(
        children: [
          Icon(icon, color: color, size: 24),
          SizedBox(width: 12),
          Text(title),
        ],
      ),
      activeColor: Colors.red.shade400,
    );
  }

  Widget _buildPhoneInput() {
    if (_selectedPaymentMethod == 'cod') return SizedBox.shrink();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: 'Phone Number',
                prefixText: '+91 ',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(Icons.phone),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your phone number';
                }
                if (value.length != 10) {
                  return 'Please enter a valid 10-digit phone number';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetails() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Details',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 12),
            _buildDetailRow('Subtotal', '₹${widget.totalAmount.toStringAsFixed(2)}'),
            _buildDetailRow('Delivery Charges', 'Free'),
            _buildDetailRow('Taxes', 'Included'),
            Divider(height: 20),
            _buildDetailRow(
              'Total Amount',
              '₹${widget.totalAmount.toStringAsFixed(2)}',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: isTotal ? 16 : 14,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
              color: isTotal ? Colors.red.shade600 : null,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _processPayment() async {
    if (_selectedPaymentMethod != 'cod' && _phoneController.text.length != 10) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please enter a valid phone number'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      bool paymentSuccess = false;

      switch (_selectedPaymentMethod) {
        case 'phonepe':
          paymentSuccess = await _processPhonePePayment();
          break;
        case 'googlepay':
          paymentSuccess = await _processGooglePayPayment();
          break;
        case 'paytm':
          paymentSuccess = await _processPaytmPayment();
          break;
        case 'upi':
          paymentSuccess = await _processUPIPayment();
          break;
        case 'cod':
          paymentSuccess = await _processCODPayment();
          break;
      }

      if (paymentSuccess) {
        // Show payment success notification
        NotificationService.showSuccessNotification(
          'Payment successful! Your order has been confirmed.'
        );
        _showOrderConfirmation();
      } else {
        // Show payment failure notification
        NotificationService.showErrorNotification(
          'Payment failed. Please try again.'
        );
        _showPaymentError();
      }
    } catch (e) {
      _showPaymentError();
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<bool> _processPhonePePayment() async {
    try {
      final result = await UPIPaymentService.payWithPhonePe(
        amount: widget.totalAmount,
        orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (result['success']) {
        _showPaymentRedirectDialog(result['method'], result['transactionRef']);
        return true;
      } else {
        NotificationService.showErrorNotification('PhonePe payment failed: ${result['error']}');
        _showErrorSnackBar(result['error']);
        return false;
      }
    } catch (e) {
      NotificationService.showErrorNotification('PhonePe payment error. Please try again.');
      _showErrorSnackBar('Error processing PhonePe payment: $e');
      return false;
    }
  }

  Future<bool> _processGooglePayPayment() async {
    try {
      final result = await UPIPaymentService.payWithGooglePay(
        amount: widget.totalAmount,
        orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (result['success']) {
        _showPaymentRedirectDialog(result['method'], result['transactionRef']);
        return true;
      } else {
        NotificationService.showErrorNotification('Google Pay payment failed: ${result['error']}');
        _showErrorSnackBar(result['error']);
        return false;
      }
    } catch (e) {
      NotificationService.showErrorNotification('Google Pay payment error. Please try again.');
      _showErrorSnackBar('Error processing Google Pay payment: $e');
      return false;
    }
  }

  Future<bool> _processPaytmPayment() async {
    try {
      final result = await UPIPaymentService.payWithPaytm(
        amount: widget.totalAmount,
        orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (result['success']) {
        _showPaymentRedirectDialog(result['method'], result['transactionRef']);
        return true;
      } else {
        NotificationService.showErrorNotification('Paytm payment failed: ${result['error']}');
        _showErrorSnackBar(result['error']);
        return false;
      }
    } catch (e) {
      NotificationService.showErrorNotification('Paytm payment error. Please try again.');
      _showErrorSnackBar('Error processing Paytm payment: $e');
      return false;
    }
  }

  Future<bool> _processUPIPayment() async {
    try {
      final result = await UPIPaymentService.payWithUPI(
        amount: widget.totalAmount,
        orderId: 'ORDER_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (result['success']) {
        _showPaymentRedirectDialog(result['method'], result['transactionRef']);
        return true;
      } else {
        NotificationService.showErrorNotification('UPI payment failed: ${result['error']}');
        _showErrorSnackBar(result['error']);
        return false;
      }
    } catch (e) {
      NotificationService.showErrorNotification('UPI payment error. Please try again.');
      _showErrorSnackBar('Error processing UPI payment: $e');
      return false;
    }
  }

  Future<bool> _processCODPayment() async {
    // Simulate COD processing
    await Future.delayed(Duration(seconds: 2));
    NotificationService.showSuccessNotification(
      'Cash on Delivery confirmed! Your order will be delivered soon.'
    );
    return true;
  }

  void _showOrderConfirmation() {
    // Show order confirmation notification
    NotificationService.showSuccessNotification(
      'Order confirmed! Your order will be delivered soon.'
    );

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => OrderConfirmationDialog(
        apartmentName: 'Your Location',
        blockName: 'Block A',
        houseNumber: '123',
        onComplete: () {
          Navigator.of(context).popUntil((route) => route.isFirst);
        },
      ),
    );
  }

  void _showPaymentError() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Payment failed. Please try again.'),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: _processPayment,
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 4),
      ),
    );
  }

  void _showPaymentRedirectDialog(String method, String transactionRef) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.payment, color: Colors.green),
            SizedBox(width: 8),
            Text('Payment Initiated'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You have been redirected to $method for payment.'),
            SizedBox(height: 12),
            Text(
              'Transaction Reference: $transactionRef',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontFamily: 'monospace',
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Please complete the payment in the $method app and return to this screen.',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue.shade600, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'If payment is successful, your order will be confirmed automatically.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Show additional confirmation notification for manual payment completion
              NotificationService.showSuccessNotification(
                'Payment confirmed! Processing your order now.'
              );
              _showOrderConfirmation(); // Simulate success for demo
            },
            child: Text('Payment Completed'),
          ),
          OutlinedButton(
            onPressed: () {
              Navigator.pop(context);
              // Show payment failure notification
              NotificationService.showErrorNotification(
                'Payment failed. Please try a different payment method.'
              );
              _showPaymentError();
            },
            child: Text('Payment Failed'),
          ),
        ],
      ),
    );
  }
}
