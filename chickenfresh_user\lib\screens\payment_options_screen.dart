import 'package:flutter/material.dart';
import '../models/product_model.dart';
import '../services/upi_payment_service.dart';
import '../services/user_profile_service.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';
import '../widgets/order_confirmation_dialog.dart';

class PaymentOptionsScreen extends StatefulWidget {
  final List<Product> products;
  final double totalAmount;
  final String? apartmentName;
  final String? blockName;
  final String? houseNumber;
  final UserProfile? userProfile;

  const PaymentOptionsScreen({
    super.key,
    required this.products,
    required this.totalAmount,
    this.apartmentName,
    this.blockName,
    this.houseNumber,
    this.userProfile,
  });

  @override
  State<PaymentOptionsScreen> createState() => _PaymentOptionsScreenState();
}

class _PaymentOptionsScreenState extends State<PaymentOptionsScreen> {
  List<String> _availableApps = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAvailableApps();
  }

  Future<void> _loadAvailableApps() async {
    final apps = await UPIPaymentService.getAvailablePaymentApps();
    setState(() {
      _availableApps = apps;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
        title: Text('Choose Payment Method'),
        elevation: 0,
      ),
      body: ChickenImageWatermark(
        child: _isLoading
            ? Center(child: CircularProgressIndicator(color: Colors.red.shade400))
            : Column(
                children: [
                  // Order Summary Card
                  Container(
                    margin: EdgeInsets.all(16),
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order Summary',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        SizedBox(height: 12),
                        ...widget.products.map((product) => Padding(
                          padding: EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  '${product.name} x ${product.quantity}',
                                  style: TextStyle(fontSize: 14),
                                ),
                              ),
                              Text(
                                '₹${(product.price * product.quantity).toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        )),
                        Divider(height: 20),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                'Total Amount',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Text(
                              '₹${widget.totalAmount.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.red.shade600,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Payment Methods
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Available Payment Methods',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          SizedBox(height: 16),
                          
                          // UPI Apps
                          if (_availableApps.isNotEmpty) ...[
                            Text(
                              'UPI Apps',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            SizedBox(height: 8),
                            ..._availableApps.map((app) => _buildPaymentAppCard(app)),
                            SizedBox(height: 16),
                          ],

                          // Other Payment Methods
                          Text(
                            'Other Methods',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          SizedBox(height: 8),
                          _buildPaymentMethodCard(
                            'Any UPI App',
                            'Choose from all UPI apps',
                            Icons.account_balance,
                            Colors.teal,
                            () => _processPayment('upi'),
                          ),
                          _buildPaymentMethodCard(
                            'Cash on Delivery',
                            'Pay when you receive',
                            Icons.money,
                            Colors.green,
                            () => _processPayment('cod'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildPaymentAppCard(String appName) {
    IconData icon;
    Color color;
    String Function() paymentMethod;

    switch (appName.toLowerCase()) {
      case 'phonepe':
        icon = Icons.phone_android;
        color = Colors.purple;
        paymentMethod = () => 'phonepe';
        break;
      case 'google pay':
        icon = Icons.payment;
        color = Colors.blue;
        paymentMethod = () => 'googlepay';
        break;
      case 'paytm':
        icon = Icons.account_balance_wallet;
        color = Colors.indigo;
        paymentMethod = () => 'paytm';
        break;
      case 'bhim':
        icon = Icons.account_balance;
        color = Colors.orange;
        paymentMethod = () => 'bhim';
        break;
      case 'amazon pay':
        icon = Icons.shopping_bag;
        color = Colors.amber.shade700;
        paymentMethod = () => 'amazonpay';
        break;
      default:
        icon = Icons.payment;
        color = Colors.grey;
        paymentMethod = () => 'upi';
    }

    return _buildPaymentMethodCard(
      appName,
      'Pay with $appName',
      icon,
      color,
      () => _processPayment(paymentMethod()),
    );
  }

  Widget _buildPaymentMethodCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.arrow_forward_ios, color: Colors.grey.shade400, size: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _processPayment(String method) async {
    // Show loading
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: Card(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: Colors.red.shade400),
                SizedBox(height: 16),
                Text('Processing payment...'),
              ],
            ),
          ),
        ),
      ),
    );

    try {
      Map<String, dynamic> result;
      final orderId = 'ORDER_${DateTime.now().millisecondsSinceEpoch}';

      switch (method) {
        case 'phonepe':
          result = await UPIPaymentService.payWithPhonePe(
            amount: widget.totalAmount,
            orderId: orderId,
          );
          break;
        case 'googlepay':
          result = await UPIPaymentService.payWithGooglePay(
            amount: widget.totalAmount,
            orderId: orderId,
          );
          break;
        case 'paytm':
          result = await UPIPaymentService.payWithPaytm(
            amount: widget.totalAmount,
            orderId: orderId,
          );
          break;
        case 'upi':
          result = await UPIPaymentService.payWithUPI(
            amount: widget.totalAmount,
            orderId: orderId,
          );
          break;
        case 'cod':
          // Simulate COD processing
          await Future.delayed(Duration(seconds: 2));
          result = {'success': true, 'method': 'Cash on Delivery'};
          break;
        default:
          result = {'success': false, 'error': 'Unknown payment method'};
      }

      Navigator.pop(context); // Close loading dialog

      if (result['success']) {
        // Show payment success notification
        NotificationService.showSuccessNotification(
          method == 'cod'
            ? 'Order placed successfully! Cash on Delivery confirmed.'
            : 'Payment successful! Your order has been placed.'
        );

        if (method == 'cod') {
          _showOrderConfirmation();
        } else {
          _showPaymentRedirectDialog(result['method'], result['transactionRef']);
        }
      } else {
        // Show payment failure notification
        NotificationService.showErrorNotification(
          'Payment failed: ${result['error']}'
        );
        _showErrorSnackBar(result['error']);
      }
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      // Show payment error notification
      NotificationService.showErrorNotification(
        'Payment failed: Unable to process payment. Please try again.'
      );
      _showErrorSnackBar('Payment error: $e');
    }
  }

  void _showPaymentRedirectDialog(String method, String transactionRef) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.payment, color: Colors.green),
            SizedBox(width: 8),
            Text('Payment Initiated'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('You have been redirected to $method for payment.'),
            SizedBox(height: 16),
            Text(
              'Please complete the payment and return to this screen.',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Show additional confirmation notification for manual payment completion
              NotificationService.showSuccessNotification(
                'Payment confirmed! Processing your order now.'
              );
              _showOrderConfirmation();
            },
            child: Text('Payment Completed'),
          ),
          OutlinedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context);
            },
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showOrderConfirmation() {
    // Show order confirmation notification
    NotificationService.showSuccessNotification(
      'Order confirmed! Your order will be delivered soon.'
    );

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => OrderConfirmationDialog(
        address: 'Your Location, Block A, House 123',
        onComplete: () {
          Navigator.of(context).popUntil((route) => route.isFirst);
        },
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 4),
      ),
    );
  }
}
