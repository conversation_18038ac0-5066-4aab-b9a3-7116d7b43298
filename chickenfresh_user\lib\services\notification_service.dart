import 'dart:convert';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:overlay_support/overlay_support.dart';

class NotificationService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  
  static const String _fcmTokenKey = 'fcm_token';
  static const String _notificationSettingsKey = 'notification_settings';

  // Initialize notification service
  static Future<void> initialize() async {
    await _initializeLocalNotifications();
    await _initializeFirebaseMessaging();
    await _requestPermissions();
  }

  // Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  // Initialize Firebase Messaging
  static Future<void> _initializeFirebaseMessaging() async {
    // Request permission for iOS
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('User granted permission');
    } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
      print('User granted provisional permission');
    } else {
      print('User declined or has not accepted permission');
    }

    // Get FCM token
    String? token = await _firebaseMessaging.getToken();
    if (token != null) {
      await _saveFCMToken(token);
      print('FCM Token: $token');
    }

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen(_saveFCMToken);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      _handleNotificationTap(initialMessage);
    }
  }

  // Request permissions
  static Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      await Permission.notification.request();
    }
  }

  // Save FCM token
  static Future<void> _saveFCMToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_fcmTokenKey, token);
  }

  // Get FCM token
  static Future<String?> getFCMToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_fcmTokenKey);
  }

  // Handle foreground messages
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Handling a foreground message: ${message.messageId}');
    
    // Show in-app notification
    showOverlayNotification(
      (context) => _buildInAppNotification(message),
      duration: const Duration(seconds: 4),
    );

    // Also show local notification
    await _showLocalNotification(message);
  }

  // Handle background messages
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Handling a background message: ${message.messageId}');
    await _showLocalNotification(message);
  }

  // Handle notification tap
  static void _handleNotificationTap(RemoteMessage message) {
    print('Notification tapped: ${message.data}');
    // Navigate to specific screen based on notification data
    _navigateBasedOnNotification(message);
  }

  // Handle local notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    print('Local notification tapped: ${response.payload}');
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      _navigateBasedOnData(data);
    }
  }

  // Show local notification
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'chickenfresh_channel',
      'ChickenFresh Notifications',
      channelDescription: 'Notifications for ChickenFresh app',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
      icon: '@mipmap/ic_launcher',
      color: Color(0xFFE53E3E), // Red color
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'ChickenFresh',
      message.notification?.body ?? 'You have a new notification',
      platformChannelSpecifics,
      payload: jsonEncode(message.data),
    );
  }

  // Build in-app notification widget
  static Widget _buildInAppNotification(RemoteMessage message) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.red.shade400, Colors.red.shade600],
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: Icon(
                _getNotificationIcon(message.data['type']),
                color: Colors.red.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    message.notification?.title ?? 'ChickenFresh',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    message.notification?.body ?? 'You have a new notification',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => OverlaySupportEntry.of(null)?.dismiss(),
              icon: const Icon(Icons.close, color: Colors.white70),
            ),
          ],
        ),
      ),
    );
  }

  // Get notification icon based on type
  static IconData _getNotificationIcon(String? type) {
    switch (type) {
      case 'order':
        return Icons.shopping_bag;
      case 'delivery':
        return Icons.local_shipping;
      case 'offer':
        return Icons.local_offer;
      case 'product':
        return Icons.restaurant;
      default:
        return Icons.notifications;
    }
  }

  // Navigate based on notification
  static void _navigateBasedOnNotification(RemoteMessage message) {
    _navigateBasedOnData(message.data);
  }

  // Navigate based on data
  static void _navigateBasedOnData(Map<String, dynamic> data) {
    // This will be implemented when we have navigation context
    // For now, just print the data
    print('Navigation data: $data');
  }

  // Show custom notification
  static Future<void> showCustomNotification({
    required String title,
    required String body,
    String? type,
    Map<String, dynamic>? data,
  }) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'chickenfresh_channel',
      'ChickenFresh Notifications',
      channelDescription: 'Notifications for ChickenFresh app',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: true,
      icon: '@mipmap/ic_launcher',
      color: Color(0xFFE53E3E),
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: data != null ? jsonEncode(data) : null,
    );
  }

  // Show in-app notification
  static void showInAppNotification({
    required String title,
    required String message,
    String? type,
    VoidCallback? onTap,
  }) {
    showOverlayNotification(
      (context) => GestureDetector(
        onTap: () {
          OverlaySupportEntry.of(context)?.dismiss();
          onTap?.call();
        },
        child: Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _getNotificationIcon(type),
                    color: Colors.green.shade600,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        message,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => OverlaySupportEntry.of(null)?.dismiss(),
                  icon: const Icon(Icons.close, color: Colors.white70),
                ),
              ],
            ),
          ),
        ),
      ),
      duration: const Duration(seconds: 4),
    );
  }

  // Get notification settings
  static Future<Map<String, bool>> getNotificationSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = prefs.getString(_notificationSettingsKey);

    if (settingsJson != null) {
      final settings = Map<String, dynamic>.from(jsonDecode(settingsJson));
      return settings.map((key, value) => MapEntry(key, value as bool));
    }

    // Default settings
    return {
      'orders': true,
      'offers': true,
      'delivery': true,
      'products': true,
      'general': true,
    };
  }

  // Save notification settings
  static Future<void> saveNotificationSettings(Map<String, bool> settings) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_notificationSettingsKey, jsonEncode(settings));
  }

  // Check if notifications are enabled for a type
  static Future<bool> isNotificationEnabled(String type) async {
    final settings = await getNotificationSettings();
    return settings[type] ?? true;
  }

  // Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  // Cancel specific notification
  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  // Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
  }

  // Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
  }

  // Show order notification
  static Future<void> showOrderNotification({
    required String orderId,
    required String status,
    String? message,
  }) async {
    if (!await isNotificationEnabled('orders')) return;

    String title = 'Order Update';
    String body = message ?? 'Your order #$orderId has been $status';

    await showCustomNotification(
      title: title,
      body: body,
      type: 'order',
      data: {
        'type': 'order',
        'orderId': orderId,
        'status': status,
      },
    );
  }

  // Show delivery notification
  static Future<void> showDeliveryNotification({
    required String orderId,
    required String status,
    String? estimatedTime,
  }) async {
    if (!await isNotificationEnabled('delivery')) return;

    String title = 'Delivery Update';
    String body = 'Your order #$orderId is $status';
    if (estimatedTime != null) {
      body += ' - ETA: $estimatedTime';
    }

    await showCustomNotification(
      title: title,
      body: body,
      type: 'delivery',
      data: {
        'type': 'delivery',
        'orderId': orderId,
        'status': status,
        'estimatedTime': estimatedTime,
      },
    );
  }

  // Show offer notification
  static Future<void> showOfferNotification({
    required String title,
    required String description,
    String? offerId,
  }) async {
    if (!await isNotificationEnabled('offers')) return;

    await showCustomNotification(
      title: title,
      body: description,
      type: 'offer',
      data: {
        'type': 'offer',
        'offerId': offerId,
      },
    );
  }

  // Show product notification
  static Future<void> showProductNotification({
    required String productName,
    required String message,
    String? productId,
  }) async {
    if (!await isNotificationEnabled('products')) return;

    await showCustomNotification(
      title: productName,
      body: message,
      type: 'product',
      data: {
        'type': 'product',
        'productId': productId,
      },
    );
  }

  // Show success notification (in-app)
  static void showSuccessNotification(String message) {
    showInAppNotification(
      title: 'Success!',
      message: message,
      type: 'success',
    );
  }

  // Show error notification (in-app)
  static void showErrorNotification(String message) {
    showInAppNotification(
      title: 'Error',
      message: message,
      type: 'error',
    );
  }

  // Show info notification (in-app)
  static void showInfoNotification(String message) {
    showInAppNotification(
      title: 'Info',
      message: message,
      type: 'info',
    );
  }
}