import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/user_profile_service.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';
import 'profile_edit_screen.dart';
import 'notification_settings_screen.dart';
import 'security_settings_screen.dart';
import 'app_preferences_screen.dart';
import 'account_management_screen.dart';

class ProfileSettingsScreen extends StatefulWidget {
  const ProfileSettingsScreen({super.key});

  @override
  State<ProfileSettingsScreen> createState() => _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends State<ProfileSettingsScreen> {
  UserProfile? _currentUserProfile;
  String? _apartmentName;
  String? _selectedBlockName;
  String? _selectedHouseNumber;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final apartment = prefs.getString('apartment');
      final block = prefs.getString('selectedBlock');
      final house = prefs.getString('selectedHouse');

      if (apartment != null && block != null && house != null) {
        final profile = await UserProfileService.getUserProfile(
          apartmentName: apartment,
          blockName: block,
          houseNumber: house,
        );

        setState(() {
          _apartmentName = apartment;
          _selectedBlockName = block;
          _selectedHouseNumber = house;
          _currentUserProfile = profile;
        });
      }
    } catch (e) {
      print('Error loading user data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Profile Settings'),
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: ChickenImageWatermark(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Header
                    _buildProfileHeader(),
                    const SizedBox(height: 24),

                    // Personal Information Section
                    _buildSection(
                      'Personal Information',
                      Icons.person,
                      Colors.blue,
                      [
                        _buildSettingsTile(
                          'Edit Profile',
                          'Update your name and phone number',
                          Icons.edit,
                          () => _navigateToProfileEdit(),
                        ),
                        _buildSettingsTile(
                          'Address Information',
                          '${_apartmentName ?? 'Not set'} - ${_selectedBlockName ?? 'Not set'} - ${_selectedHouseNumber ?? 'Not set'}',
                          Icons.location_on,
                          () => _showAddressInfo(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Preferences Section
                    _buildSection(
                      'Preferences',
                      Icons.settings,
                      Colors.green,
                      [
                        _buildSettingsTile(
                          'Notification Settings',
                          'Manage your notification preferences',
                          Icons.notifications,
                          () => _navigateToNotificationSettings(),
                        ),
                        _buildSettingsTile(
                          'App Preferences',
                          'Theme, language, and display settings',
                          Icons.palette,
                          () => _navigateToAppPreferences(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Security Section
                    _buildSection(
                      'Security & Privacy',
                      Icons.security,
                      Colors.orange,
                      [
                        _buildSettingsTile(
                          'Security Settings',
                          'Change PIN, security preferences',
                          Icons.lock,
                          () => _navigateToSecuritySettings(),
                        ),
                        _buildSettingsTile(
                          'Privacy Settings',
                          'Data usage and privacy controls',
                          Icons.privacy_tip,
                          () => _showPrivacySettings(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Account Section
                    _buildSection(
                      'Account Management',
                      Icons.account_circle,
                      Colors.purple,
                      [
                        _buildSettingsTile(
                          'Account Information',
                          'View and manage your account',
                          Icons.info,
                          () => _navigateToAccountManagement(),
                        ),
                        _buildSettingsTile(
                          'Order History',
                          'View your past orders',
                          Icons.history,
                          () => _showOrderHistory(),
                        ),
                        _buildSettingsTile(
                          'Help & Support',
                          'Get help and contact support',
                          Icons.help,
                          () => _showHelpSupport(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // App Information Section
                    _buildSection(
                      'App Information',
                      Icons.info_outline,
                      Colors.grey,
                      [
                        _buildSettingsTile(
                          'About ChickenFresh',
                          'App version and information',
                          Icons.info,
                          () => _showAboutApp(),
                        ),
                        _buildSettingsTile(
                          'Terms & Conditions',
                          'Read our terms and conditions',
                          Icons.description,
                          () => _showTermsConditions(),
                        ),
                        _buildSettingsTile(
                          'Privacy Policy',
                          'Read our privacy policy',
                          Icons.policy,
                          () => _showPrivacyPolicy(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Logout Button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _showLogoutDialog,
                        icon: const Icon(Icons.logout),
                        label: const Text('Logout'),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.red.shade400),
                          foregroundColor: Colors.red.shade600,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.red.shade400, Colors.red.shade600],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 70,
            height: 70,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person,
              size: 40,
              color: Colors.red.shade400,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentUserProfile?.name ?? 'Guest User',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentUserProfile?.phoneNumber ?? 'No phone number',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Member since ${_currentUserProfile?.createdAt.year ?? 'Unknown'}',
                  style: const TextStyle(
                    color: Colors.white60,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _navigateToProfileEdit(),
            icon: const Icon(
              Icons.edit,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    String title,
    IconData icon,
    MaterialColor color,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color.shade600, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingsTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey.shade400,
      ),
      onTap: onTap,
    );
  }

  // Navigation methods
  void _navigateToProfileEdit() {
    if (_currentUserProfile != null &&
        _apartmentName != null &&
        _selectedBlockName != null &&
        _selectedHouseNumber != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProfileEditScreen(
            apartmentName: _apartmentName!,
            blockName: _selectedBlockName!,
            houseNumber: _selectedHouseNumber!,
            currentProfile: _currentUserProfile,
            onProfileUpdated: () {
              _loadUserData();
              Navigator.of(context).pop();
              NotificationService.showSuccessNotification(
                'Profile updated successfully!',
              );
            },
          ),
        ),
      );
    } else {
      NotificationService.showErrorNotification(
        'Please select your address first',
      );
    }
  }

  void _navigateToNotificationSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotificationSettingsScreen(),
      ),
    );
  }

  void _navigateToSecuritySettings() {
    if (_currentUserProfile != null &&
        _apartmentName != null &&
        _selectedBlockName != null &&
        _selectedHouseNumber != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SecuritySettingsScreen(
            apartmentName: _apartmentName!,
            blockName: _selectedBlockName!,
            houseNumber: _selectedHouseNumber!,
            currentProfile: _currentUserProfile!,
          ),
        ),
      );
    } else {
      NotificationService.showErrorNotification(
        'Please select your address first',
      );
    }
  }

  void _navigateToAppPreferences() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AppPreferencesScreen(),
      ),
    );
  }

  void _navigateToAccountManagement() {
    if (_currentUserProfile != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => AccountManagementScreen(
            userProfile: _currentUserProfile!,
            apartmentName: _apartmentName!,
            blockName: _selectedBlockName!,
            houseNumber: _selectedHouseNumber!,
          ),
        ),
      );
    } else {
      NotificationService.showErrorNotification(
        'Please select your address first',
      );
    }
  }

  void _showAddressInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Address Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('Full Address',
              _apartmentName != null && _selectedBlockName != null && _selectedHouseNumber != null
                ? '$_apartmentName, Block $_selectedBlockName, House $_selectedHouseNumber'
                : 'Address not set'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPrivacySettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Settings'),
        content: const Text(
          'Privacy settings will be available in a future update. '
          'Your data is secure and we follow strict privacy guidelines.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showOrderHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Order History'),
        content: const Text(
          'Order history feature will be available in a future update. '
          'You will be able to view all your past orders here.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showHelpSupport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Need help? Contact us:'),
            const SizedBox(height: 12),
            _buildInfoRow('Phone', '+91 9876543210'),
            _buildInfoRow('Email', '<EMAIL>'),
            _buildInfoRow('Hours', '9 AM - 9 PM'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAboutApp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About ChickenFresh'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'ChickenFresh User App',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text(
              'Fresh chicken delivery service for your apartment complex. '
              'Order premium quality chicken products with ease.',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showTermsConditions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Terms & Conditions'),
        content: const SingleChildScrollView(
          child: Text(
            'Terms and Conditions for ChickenFresh:\n\n'
            '1. Service Usage: This app is for ordering chicken products within registered apartment complexes.\n\n'
            '2. Account Security: Users are responsible for maintaining the security of their PIN.\n\n'
            '3. Order Policy: Orders are subject to availability and delivery schedules.\n\n'
            '4. Payment: Payment must be completed before delivery.\n\n'
            '5. Quality Guarantee: We ensure fresh, high-quality products.\n\n'
            'For complete terms, visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'Privacy Policy for ChickenFresh:\n\n'
            '1. Data Collection: We collect only necessary information for service delivery.\n\n'
            '2. Data Usage: Your data is used solely for order processing and delivery.\n\n'
            '3. Data Security: We implement strong security measures to protect your data.\n\n'
            '4. Data Sharing: We do not share your personal data with third parties.\n\n'
            '5. Contact Information: Used only for delivery coordination.\n\n'
            'For complete privacy policy, visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _logout();
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  Future<void> _logout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );
        NotificationService.showInfoNotification(
          'You have been logged out successfully',
        );
      }
    } catch (e) {
      NotificationService.showErrorNotification(
        'Error logging out: $e',
      );
    }
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
