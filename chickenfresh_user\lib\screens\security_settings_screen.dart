import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/user_profile_service.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';

class SecuritySettingsScreen extends StatefulWidget {
  final String apartmentName;
  final String blockName;
  final String houseNumber;
  final UserProfile currentProfile;

  const SecuritySettingsScreen({
    super.key,
    required this.apartmentName,
    required this.blockName,
    required this.houseNumber,
    required this.currentProfile,
  });

  @override
  State<SecuritySettingsScreen> createState() => _SecuritySettingsScreenState();
}

class _SecuritySettingsScreenState extends State<SecuritySettingsScreen> {
  final _currentPinController = TextEditingController();
  final _newPinController = TextEditingController();
  final _confirmPinController = TextEditingController();
  
  bool _isChangingPin = false;
  bool _isLoading = false;
  bool _obscureCurrentPin = true;
  bool _obscureNewPin = true;
  bool _obscureConfirmPin = true;

  @override
  void dispose() {
    _currentPinController.dispose();
    _newPinController.dispose();
    _confirmPinController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Security Settings'),
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
      ),
      body: ChickenImageWatermark(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Security Overview
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade50, Colors.orange.shade100],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.security, color: Colors.orange.shade600, size: 28),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Account Security',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Manage your account security settings',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // PIN Management Section
              _buildSection(
                'PIN Management',
                Icons.lock,
                Colors.blue,
                [
                  _buildSecurityTile(
                    'Change PIN',
                    'Update your 4-digit security PIN',
                    Icons.edit,
                    () => _toggleChangePinMode(),
                  ),
                  if (_isChangingPin) _buildChangePinForm(),
                ],
              ),
              const SizedBox(height: 20),

              // Account Information Section
              _buildSection(
                'Account Information',
                Icons.info,
                Colors.green,
                [
                  _buildInfoTile('Name', widget.currentProfile.name),
                  _buildInfoTile('Phone', widget.currentProfile.phoneNumber),
                  _buildInfoTile('Address', '${widget.apartmentName} - ${widget.blockName} - ${widget.houseNumber}'),
                  _buildInfoTile('Member Since', _formatDate(widget.currentProfile.createdAt)),
                ],
              ),
              const SizedBox(height: 20),

              // Security Tips Section
              _buildSection(
                'Security Tips',
                Icons.tips_and_updates,
                Colors.purple,
                [
                  _buildTipTile(
                    'Keep your PIN secure',
                    'Never share your PIN with anyone',
                    Icons.visibility_off,
                  ),
                  _buildTipTile(
                    'Use a unique PIN',
                    'Don\'t use common PINs like 1234 or 0000',
                    Icons.shuffle,
                  ),
                  _buildTipTile(
                    'Regular updates',
                    'Change your PIN periodically for better security',
                    Icons.update,
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Delete Account Section
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.red.shade600),
                        const SizedBox(width: 8),
                        Text(
                          'Danger Zone',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.red.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Once you delete your account, there is no going back. Please be certain.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade700,
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _showDeleteAccountDialog,
                        icon: const Icon(Icons.delete_forever),
                        label: const Text('Delete Account'),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.red.shade400),
                          foregroundColor: Colors.red.shade600,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    IconData icon,
    MaterialColor color,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color.shade600, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSecurityTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey.shade400,
      ),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTipTile(String title, String description, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.purple.shade600, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChangePinForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Change Your PIN',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Current PIN
          TextFormField(
            controller: _currentPinController,
            obscureText: _obscureCurrentPin,
            keyboardType: TextInputType.number,
            maxLength: 4,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              labelText: 'Current PIN',
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(_obscureCurrentPin ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscureCurrentPin = !_obscureCurrentPin),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              counterText: '',
            ),
          ),
          const SizedBox(height: 16),

          // New PIN
          TextFormField(
            controller: _newPinController,
            obscureText: _obscureNewPin,
            keyboardType: TextInputType.number,
            maxLength: 4,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              labelText: 'New PIN',
              prefixIcon: const Icon(Icons.lock),
              suffixIcon: IconButton(
                icon: Icon(_obscureNewPin ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscureNewPin = !_obscureNewPin),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              counterText: '',
            ),
          ),
          const SizedBox(height: 16),

          // Confirm PIN
          TextFormField(
            controller: _confirmPinController,
            obscureText: _obscureConfirmPin,
            keyboardType: TextInputType.number,
            maxLength: 4,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              labelText: 'Confirm New PIN',
              prefixIcon: const Icon(Icons.lock),
              suffixIcon: IconButton(
                icon: Icon(_obscureConfirmPin ? Icons.visibility : Icons.visibility_off),
                onPressed: () => setState(() => _obscureConfirmPin = !_obscureConfirmPin),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              counterText: '',
            ),
          ),
          const SizedBox(height: 20),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _cancelChangePin,
                  child: const Text('Cancel'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _changePin,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Update PIN'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _toggleChangePinMode() {
    setState(() {
      _isChangingPin = !_isChangingPin;
      if (!_isChangingPin) {
        _clearPinFields();
      }
    });
  }

  void _cancelChangePin() {
    setState(() {
      _isChangingPin = false;
    });
    _clearPinFields();
  }

  void _clearPinFields() {
    _currentPinController.clear();
    _newPinController.clear();
    _confirmPinController.clear();
  }

  Future<void> _changePin() async {
    final currentPin = _currentPinController.text;
    final newPin = _newPinController.text;
    final confirmPin = _confirmPinController.text;

    // Validation
    if (currentPin.length != 4) {
      NotificationService.showErrorNotification('Please enter your current PIN');
      return;
    }

    if (newPin.length != 4) {
      NotificationService.showErrorNotification('New PIN must be 4 digits');
      return;
    }

    if (newPin != confirmPin) {
      NotificationService.showErrorNotification('New PIN and confirmation do not match');
      return;
    }

    if (currentPin == newPin) {
      NotificationService.showErrorNotification('New PIN must be different from current PIN');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Verify current PIN
      final isCurrentPinValid = await UserProfileService.verifyPin(
        pin: currentPin,
        apartmentName: widget.apartmentName,
        blockName: widget.blockName,
        houseNumber: widget.houseNumber,
      );

      if (!isCurrentPinValid) {
        NotificationService.showErrorNotification('Current PIN is incorrect');
        return;
      }

      // Update PIN
      await UserProfileService.updateUserProfile(
        apartmentName: widget.apartmentName,
        blockName: widget.blockName,
        houseNumber: widget.houseNumber,
        newPin: newPin,
      );

      NotificationService.showSuccessNotification('PIN updated successfully!');
      _cancelChangePin();
    } catch (e) {
      NotificationService.showErrorNotification('Failed to update PIN: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Account'),
        content: const Text(
          'Are you sure you want to delete your account? This action cannot be undone. '
          'All your data will be permanently removed.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showDeleteConfirmationDialog();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmationDialog() {
    final pinController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Account Deletion'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Enter your PIN to confirm account deletion:'),
            const SizedBox(height: 16),
            TextFormField(
              controller: pinController,
              obscureText: true,
              keyboardType: TextInputType.number,
              maxLength: 4,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                labelText: 'Enter PIN',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                counterText: '',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final pin = pinController.text;
              if (pin.length == 4) {
                Navigator.of(context).pop();
                await _deleteAccount(pin);
              } else {
                NotificationService.showErrorNotification('Please enter a valid PIN');
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAccount(String pin) async {
    try {
      // Verify PIN
      final isValid = await UserProfileService.verifyPin(
        pin: pin,
        apartmentName: widget.apartmentName,
        blockName: widget.blockName,
        houseNumber: widget.houseNumber,
      );

      if (!isValid) {
        NotificationService.showErrorNotification('Incorrect PIN');
        return;
      }

      // Delete account
      await UserProfileService.deleteUserProfile(
        apartmentName: widget.apartmentName,
        blockName: widget.blockName,
        houseNumber: widget.houseNumber,
      );

      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          '/login',
          (route) => false,
        );
        NotificationService.showInfoNotification(
          'Account deleted successfully',
        );
      }
    } catch (e) {
      NotificationService.showErrorNotification('Failed to delete account: $e');
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
