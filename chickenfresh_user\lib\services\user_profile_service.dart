import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';

class UserProfile {
  final String name;
  final String phoneNumber;
  final String hashedPin;
  final String apartmentName;
  final String blockName;
  final String houseNumber;
  final DateTime createdAt;

  UserProfile({
    required this.name,
    required this.phoneNumber,
    required this.hashedPin,
    required this.apartmentName,
    required this.blockName,
    required this.houseNumber,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'hashedPin': hashedPin,
      'apartmentName': apartmentName,
      'blockName': blockName,
      'houseNumber': houseNumber,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      name: json['name'],
      phoneNumber: json['phoneNumber'],
      hashedPin: json['hashedPin'],
      apartmentName: json['apartmentName'],
      blockName: json['blockName'],
      houseNumber: json['houseNumber'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  String get addressKey => '${apartmentName}_${blockName}_$houseNumber';
}

class UserProfileService {
  static const String _userProfilesKey = 'user_profiles';

  // Hash PIN for security
  static String _hashPin(String pin) {
    var bytes = utf8.encode(pin);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Save user profile for a specific address
  static Future<void> saveUserProfile({
    required String name,
    required String phoneNumber,
    required String pin,
    required String apartmentName,
    required String blockName,
    required String houseNumber,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    
    final profile = UserProfile(
      name: name,
      phoneNumber: phoneNumber,
      hashedPin: _hashPin(pin),
      apartmentName: apartmentName,
      blockName: blockName,
      houseNumber: houseNumber,
      createdAt: DateTime.now(),
    );

    // Get existing profiles
    final existingProfilesJson = prefs.getString(_userProfilesKey) ?? '{}';
    final Map<String, dynamic> profiles = jsonDecode(existingProfilesJson);
    
    // Add new profile with address as key
    profiles[profile.addressKey] = profile.toJson();
    
    // Save back to preferences
    await prefs.setString(_userProfilesKey, jsonEncode(profiles));
  }

  // Get user profile for a specific address
  static Future<UserProfile?> getUserProfile({
    required String apartmentName,
    required String blockName,
    required String houseNumber,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final profilesJson = prefs.getString(_userProfilesKey);
    
    if (profilesJson == null) return null;
    
    final Map<String, dynamic> profiles = jsonDecode(profilesJson);
    final addressKey = '${apartmentName}_${blockName}_$houseNumber';
    
    if (profiles.containsKey(addressKey)) {
      return UserProfile.fromJson(profiles[addressKey]);
    }
    
    return null;
  }

  // Verify PIN for a specific address
  static Future<bool> verifyPin({
    required String pin,
    required String apartmentName,
    required String blockName,
    required String houseNumber,
  }) async {
    final profile = await getUserProfile(
      apartmentName: apartmentName,
      blockName: blockName,
      houseNumber: houseNumber,
    );
    
    if (profile == null) return false;
    
    return profile.hashedPin == _hashPin(pin);
  }

  // Check if user exists for a specific address
  static Future<bool> userExistsForAddress({
    required String apartmentName,
    required String blockName,
    required String houseNumber,
  }) async {
    final profile = await getUserProfile(
      apartmentName: apartmentName,
      blockName: blockName,
      houseNumber: houseNumber,
    );
    return profile != null;
  }

  // Update user profile (for name/phone changes)
  static Future<void> updateUserProfile({
    required String apartmentName,
    required String blockName,
    required String houseNumber,
    String? newName,
    String? newPhoneNumber,
    String? newPin,
  }) async {
    final existingProfile = await getUserProfile(
      apartmentName: apartmentName,
      blockName: blockName,
      houseNumber: houseNumber,
    );
    
    if (existingProfile == null) return;
    
    final updatedProfile = UserProfile(
      name: newName ?? existingProfile.name,
      phoneNumber: newPhoneNumber ?? existingProfile.phoneNumber,
      hashedPin: newPin != null ? _hashPin(newPin) : existingProfile.hashedPin,
      apartmentName: apartmentName,
      blockName: blockName,
      houseNumber: houseNumber,
      createdAt: existingProfile.createdAt,
    );
    
    final prefs = await SharedPreferences.getInstance();
    final existingProfilesJson = prefs.getString(_userProfilesKey) ?? '{}';
    final Map<String, dynamic> profiles = jsonDecode(existingProfilesJson);
    
    profiles[updatedProfile.addressKey] = updatedProfile.toJson();
    await prefs.setString(_userProfilesKey, jsonEncode(profiles));
  }

  // Delete user profile for a specific address
  static Future<void> deleteUserProfile({
    required String apartmentName,
    required String blockName,
    required String houseNumber,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    final existingProfilesJson = prefs.getString(_userProfilesKey) ?? '{}';
    final Map<String, dynamic> profiles = jsonDecode(existingProfilesJson);
    
    final addressKey = '${apartmentName}_${blockName}_$houseNumber';
    profiles.remove(addressKey);
    
    await prefs.setString(_userProfilesKey, jsonEncode(profiles));
  }

  // Get all user profiles (for debugging or management)
  static Future<List<UserProfile>> getAllUserProfiles() async {
    final prefs = await SharedPreferences.getInstance();
    final profilesJson = prefs.getString(_userProfilesKey);
    
    if (profilesJson == null) return [];
    
    final Map<String, dynamic> profiles = jsonDecode(profilesJson);
    return profiles.values
        .map((profileData) => UserProfile.fromJson(profileData))
        .toList();
  }
}
