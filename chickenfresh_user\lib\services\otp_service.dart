import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class OTPService {
  static const bool _isDevelopmentMode = kDebugMode; // Use debug mode for development
  static const String _testOTP = '123456'; // Test OTP for development
  
  static Future<OTPResult> sendOTP(String phoneNumber) async {
    if (_isDevelopmentMode) {
      // In development mode, simulate OTP sending
      await Future.delayed(const Duration(seconds: 1)); // Simulate network delay
      return OTPResult(
        success: true,
        verificationId: 'dev_verification_id',
        message: 'Development mode: Use OTP 123456',
        isDevelopmentMode: true,
      );
    }
    
    try {
      // Production mode - use Firebase Auth
      String? verificationId;
      bool codeSent = false;
      String? errorMessage;
      
      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          // This won't be called in our flow since we handle manual verification
        },
        verificationFailed: (FirebaseAuthException e) {
          errorMessage = e.message ?? 'Verification failed';
        },
        codeSent: (String vId, int? resendToken) {
          verificationId = vId;
          codeSent = true;
        },
        codeAutoRetrievalTimeout: (String vId) {
          verificationId = vId;
        },
        timeout: const Duration(seconds: 60),
      );
      
      // Wait a bit for the callbacks to complete
      await Future.delayed(const Duration(seconds: 2));
      
      if (errorMessage != null) {
        return OTPResult(
          success: false,
          message: errorMessage!,
          isDevelopmentMode: false,
        );
      }
      
      if (codeSent && verificationId != null) {
        return OTPResult(
          success: true,
          verificationId: verificationId!,
          message: 'OTP sent successfully',
          isDevelopmentMode: false,
        );
      }
      
      return OTPResult(
        success: false,
        message: 'Failed to send OTP',
        isDevelopmentMode: false,
      );
      
    } catch (e) {
      return OTPResult(
        success: false,
        message: 'Error: $e',
        isDevelopmentMode: false,
      );
    }
  }
  
  static Future<bool> verifyOTP(String verificationId, String otp) async {
    if (_isDevelopmentMode) {
      // In development mode, accept the test OTP
      return otp == _testOTP;
    }
    
    try {
      // Production mode - verify with Firebase
      final credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: otp,
      );
      
      await FirebaseAuth.instance.signInWithCredential(credential);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('OTP verification failed: $e');
      }
      return false;
    }
  }
  
  static bool get isDevelopmentMode => _isDevelopmentMode;
  static String get testOTP => _testOTP;
}

class OTPResult {
  final bool success;
  final String? verificationId;
  final String message;
  final bool isDevelopmentMode;
  
  OTPResult({
    required this.success,
    this.verificationId,
    required this.message,
    required this.isDevelopmentMode,
  });
}
