import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/user_profile_service.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';

class AccountManagementScreen extends StatefulWidget {
  final UserProfile userProfile;
  final String apartmentName;
  final String blockName;
  final String houseNumber;

  const AccountManagementScreen({
    super.key,
    required this.userProfile,
    required this.apartmentName,
    required this.blockName,
    required this.houseNumber,
  });

  @override
  State<AccountManagementScreen> createState() => _AccountManagementScreenState();
}

class _AccountManagementScreenState extends State<AccountManagementScreen> {
  Map<String, dynamic> _accountStats = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAccountStats();
  }

  Future<void> _loadAccountStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Simulate loading account statistics
      await Future.delayed(const Duration(seconds: 1));
      
      setState(() {
        _accountStats = {
          'totalOrders': prefs.getInt('total_orders') ?? 0,
          'totalSpent': prefs.getDouble('total_spent') ?? 0.0,
          'favoriteProducts': prefs.getStringList('favorite_products') ?? [],
          'lastOrderDate': prefs.getString('last_order_date'),
          'loyaltyPoints': prefs.getInt('loyalty_points') ?? 0,
          'accountStatus': 'Active',
        };
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      NotificationService.showErrorNotification('Failed to load account data');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Account Management'),
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
      ),
      body: ChickenImageWatermark(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Account Overview
                    _buildAccountOverview(),
                    const SizedBox(height: 24),

                    // Account Statistics
                    _buildAccountStats(),
                    const SizedBox(height: 20),

                    // Account Information
                    _buildAccountInfo(),
                    const SizedBox(height: 20),

                    // Account Actions
                    _buildAccountActions(),
                    const SizedBox(height: 20),

                    // Data Export
                    _buildDataExport(),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildAccountOverview() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade400, Colors.blue.shade600],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.account_circle,
                  size: 35,
                  color: Colors.blue.shade600,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.userProfile.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      widget.userProfile.phoneNumber,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      'Status: ${_accountStats['accountStatus'] ?? 'Active'}',
                      style: const TextStyle(
                        color: Colors.white60,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Orders',
                  '${_accountStats['totalOrders'] ?? 0}',
                  Icons.shopping_bag,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Spent',
                  '₹${(_accountStats['totalSpent'] ?? 0.0).toStringAsFixed(0)}',
                  Icons.currency_rupee,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Points',
                  '${_accountStats['loyaltyPoints'] ?? 0}',
                  Icons.stars,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountStats() {
    return _buildSection(
      'Account Statistics',
      Icons.analytics,
      Colors.green,
      [
        _buildInfoTile('Total Orders', '${_accountStats['totalOrders'] ?? 0}'),
        _buildInfoTile('Total Spent', '₹${(_accountStats['totalSpent'] ?? 0.0).toStringAsFixed(2)}'),
        _buildInfoTile('Loyalty Points', '${_accountStats['loyaltyPoints'] ?? 0}'),
        _buildInfoTile('Last Order', _accountStats['lastOrderDate'] ?? 'No orders yet'),
        _buildInfoTile('Favorite Products', '${(_accountStats['favoriteProducts'] as List?)?.length ?? 0} items'),
      ],
    );
  }

  Widget _buildAccountInfo() {
    return _buildSection(
      'Account Information',
      Icons.info,
      Colors.blue,
      [
        _buildInfoTile('Name', widget.userProfile.name),
        _buildInfoTile('Phone Number', widget.userProfile.phoneNumber),
        _buildInfoTile('Address', '${widget.apartmentName}, Block ${widget.blockName}, House ${widget.houseNumber}'),
        _buildInfoTile('Member Since', _formatDate(widget.userProfile.createdAt)),
        _buildInfoTile('Account Status', _accountStats['accountStatus'] ?? 'Active'),
      ],
    );
  }

  Widget _buildAccountActions() {
    return _buildSection(
      'Account Actions',
      Icons.settings,
      Colors.orange,
      [
        _buildActionTile(
          'Download Account Data',
          'Export your account information',
          Icons.download,
          () => _downloadAccountData(),
        ),
        _buildActionTile(
          'Request Data Deletion',
          'Request deletion of your personal data',
          Icons.delete_outline,
          () => _requestDataDeletion(),
        ),
        _buildActionTile(
          'Account Verification',
          'Verify your account for additional features',
          Icons.verified,
          () => _showAccountVerification(),
        ),
      ],
    );
  }

  Widget _buildDataExport() {
    return _buildSection(
      'Data & Privacy',
      Icons.privacy_tip,
      Colors.purple,
      [
        _buildActionTile(
          'Privacy Settings',
          'Manage your privacy preferences',
          Icons.privacy_tip,
          () => _showPrivacySettings(),
        ),
        _buildActionTile(
          'Data Usage',
          'View how your data is used',
          Icons.data_usage,
          () => _showDataUsage(),
        ),
        _buildActionTile(
          'Export Data',
          'Download a copy of your data',
          Icons.file_download,
          () => _exportData(),
        ),
      ],
    );
  }

  Widget _buildSection(
    String title,
    IconData icon,
    MaterialColor color,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color.shade600, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoTile(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey.shade400,
      ),
      onTap: onTap,
    );
  }

  void _downloadAccountData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Account Data'),
        content: const Text(
          'Your account data will be prepared and sent to your registered email address. '
          'This may take a few minutes to process.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              NotificationService.showSuccessNotification(
                'Account data download request submitted!',
              );
            },
            child: const Text('Request Download'),
          ),
        ],
      ),
    );
  }

  void _requestDataDeletion() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Request Data Deletion'),
        content: const Text(
          'Are you sure you want to request deletion of your personal data? '
          'This action cannot be undone and will permanently remove all your information.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showDataDeletionConfirmation();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Request Deletion'),
          ),
        ],
      ),
    );
  }

  void _showDataDeletionConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Data Deletion'),
        content: const Text(
          'Your data deletion request has been submitted. '
          'You will receive a confirmation email within 24 hours. '
          'Your account will be deactivated during the review process.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAccountVerification() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Account Verification'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Verify your account to unlock additional features:'),
            SizedBox(height: 12),
            Text('• Priority customer support'),
            Text('• Exclusive offers and discounts'),
            Text('• Early access to new products'),
            Text('• Enhanced order tracking'),
            SizedBox(height: 12),
            Text('Verification requires additional documentation.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Later'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              NotificationService.showInfoNotification(
                'Account verification feature coming soon!',
              );
            },
            child: const Text('Start Verification'),
          ),
        ],
      ),
    );
  }

  void _showPrivacySettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Settings'),
        content: const Text(
          'Privacy settings allow you to control how your data is used. '
          'This feature will be available in a future update.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDataUsage() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Data Usage'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'How we use your data:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Order processing and delivery'),
              Text('• Customer support and communication'),
              Text('• Product recommendations'),
              Text('• Service improvement and analytics'),
              Text('• Security and fraud prevention'),
              SizedBox(height: 12),
              Text(
                'We do not share your personal data with third parties without your consent.',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text(
          'Your data will be compiled into a downloadable file and sent to your email address. '
          'This includes your profile information, order history, and preferences.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              NotificationService.showSuccessNotification(
                'Data export request submitted!',
              );
            },
            child: const Text('Export'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
